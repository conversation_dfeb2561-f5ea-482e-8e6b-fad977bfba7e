-- R<PERSON>lý test script pro okamžité ov<PERSON><PERSON><PERSON><PERSON> funkčnosti
local QBX = exports.qbx_core:GetCoreObject()

-- Rychlý test všech hlavních funkcí
local function QuickTest()
    print("^2[QUICK TEST] ^7Spouštění rychlého testu...")
    
    local results = {
        vehicles = false,
        weapons = false,
        locations = false,
        qbx_shared = false,
        exports = false
    }
    
    -- Test 1: Vehicles
    local vehicles = exports.qbx_core:GetVehiclesByName()
    if vehicles and next(vehicles) then
        local count = 0
        for _ in pairs(vehicles) do count = count + 1 end
        print(string.format("^2[QUICK TEST] ^7✓ Vehicles: %d načteno", count))
        results.vehicles = true
    else
        print("^1[QUICK TEST] ^7✗ Vehicles: CHYBA")
    end
    
    -- Test 2: Weapons
    local weapons = exports.qbx_core:GetWeapons()
    if weapons and next(weapons) then
        local count = 0
        for _ in pairs(weapons) do count = count + 1 end
        print(string.format("^2[QUICK TEST] ^7✓ Weapons: %d načteno", count))
        results.weapons = true
    else
        print("^1[QUICK TEST] ^7✗ Weapons: CHYBA")
    end
    
    -- Test 3: Locations
    local locations = exports.qbx_core:GetLocations()
    if locations then
        local count = 0
        for _ in pairs(locations) do count = count + 1 end
        print(string.format("^2[QUICK TEST] ^7✓ Locations: %d načteno", count))
        results.locations = true
    else
        print("^1[QUICK TEST] ^7✗ Locations: CHYBA")
    end
    
    -- Test 4: QBX.Shared
    if QBX and QBX.Shared and QBX.Shared.Vehicles then
        print("^2[QUICK TEST] ^7✓ QBX.Shared: Dostupný")
        results.qbx_shared = true
    else
        print("^1[QUICK TEST] ^7✗ QBX.Shared: CHYBA")
    end
    
    -- Test 5: Export funkce
    local testVehicle = exports.qbx_core:GetVehiclesByName('sunrise1')
    if testVehicle and testVehicle.name then
        print(string.format("^2[QUICK TEST] ^7✓ Exports: Test vozidlo '%s' nalezeno", testVehicle.name))
        results.exports = true
    else
        print("^1[QUICK TEST] ^7✗ Exports: CHYBA")
    end
    
    -- Celkové vyhodnocení
    local passed = 0
    local total = 0
    for _, result in pairs(results) do
        total = total + 1
        if result then passed = passed + 1 end
    end
    
    print("^2[QUICK TEST] ^7" .. string.rep("=", 50))
    if passed == total then
        print("^2[QUICK TEST] ^7🎉 VŠECHNY TESTY PROŠLY! (" .. passed .. "/" .. total .. ")")
        print("^2[QUICK TEST] ^7QBX shared systém funguje správně!")
    else
        print("^1[QUICK TEST] ^7❌ NĚKTERÉ TESTY SELHALY! (" .. passed .. "/" .. total .. ")")
        print("^1[QUICK TEST] ^7Zkontroluj qbx_core a shared soubory!")
    end
    print("^2[QUICK TEST] ^7" .. string.rep("=", 50))
    
    return results, passed, total
end

-- Registrace příkazu
RegisterCommand('quicktest', function()
    QuickTest()
end, false)

-- Export pro použití z jiných scriptů
_G.QuickTest = QuickTest

-- Automatické spuštění při načtení (s delším čekáním)
CreateThread(function()
    Wait(3000)
    print("^2[QUICK TEST] ^7Použij /quicktest pro rychlé ověření funkčnosti QBX shared")
end)
