-- <PERSON><PERSON><PERSON><PERSON> test script pro okamž<PERSON> ov<PERSON><PERSON><PERSON><PERSON>ti
local QBCore = exports['qb-core']:GetCoreObject()

-- Rychlý test všech hlavních funkcí
local function QuickTest()
    print("^2[QUICK TEST] ^7Spouštění rychlého testu...")

    local results = {
        vehicles = false,
        weapons = false,
        jobs = false,
        gangs = false,
        locations = false,
        qbcore_shared = false
    }

    -- Test 1: Vehicles
    if QBCore and QBCore.Shared and QBCore.Shared.Vehicles and next(QBCore.Shared.Vehicles) then
        local count = 0
        for _ in pairs(QBCore.Shared.Vehicles) do count = count + 1 end
        print(string.format("^2[QUICK TEST] ^7✓ Vehicles: %d načteno", count))
        results.vehicles = true
    else
        print("^1[QUICK TEST] ^7✗ Vehicles: CHYBA")
    end

    -- Test 2: Weapons
    if QBCore and QBCore.Shared and QBCore.Shared.Weapons and next(QBCore.Shared.Weapons) then
        local count = 0
        for _ in pairs(QBCore.Shared.Weapons) do count = count + 1 end
        print(string.format("^2[QUICK TEST] ^7✓ Weapons: %d načteno", count))
        results.weapons = true
    else
        print("^1[QUICK TEST] ^7✗ Weapons: CHYBA")
    end

    -- Test 3: Jobs
    if QBCore and QBCore.Shared and QBCore.Shared.Jobs and next(QBCore.Shared.Jobs) then
        local count = 0
        for _ in pairs(QBCore.Shared.Jobs) do count = count + 1 end
        print(string.format("^2[QUICK TEST] ^7✓ Jobs: %d načteno", count))
        results.jobs = true
    else
        print("^1[QUICK TEST] ^7✗ Jobs: CHYBA")
    end

    -- Test 4: Gangs
    if QBCore and QBCore.Shared and QBCore.Shared.Gangs then
        local count = 0
        for _ in pairs(QBCore.Shared.Gangs) do count = count + 1 end
        print(string.format("^2[QUICK TEST] ^7✓ Gangs: %d načteno", count))
        results.gangs = true
    else
        print("^1[QUICK TEST] ^7✗ Gangs: CHYBA")
    end

    -- Test 5: Locations
    if QBCore and QBCore.Shared and QBCore.Shared.Locations then
        local count = 0
        for _ in pairs(QBCore.Shared.Locations) do count = count + 1 end
        print(string.format("^2[QUICK TEST] ^7✓ Locations: %d načteno", count))
        results.locations = true
    else
        print("^1[QUICK TEST] ^7✗ Locations: CHYBA")
    end

    -- Test 6: QBCore.Shared
    if QBCore and QBCore.Shared then
        print("^2[QUICK TEST] ^7✓ QBCore.Shared: Dostupný")
        results.qbcore_shared = true
    else
        print("^1[QUICK TEST] ^7✗ QBCore.Shared: CHYBA")
    end
    
    -- Celkové vyhodnocení
    local passed = 0
    local total = 0
    for _, result in pairs(results) do
        total = total + 1
        if result then passed = passed + 1 end
    end
    
    print("^2[QUICK TEST] ^7" .. string.rep("=", 50))
    if passed == total then
        print("^2[QUICK TEST] ^7🎉 VŠECHNY TESTY PROŠLY! (" .. passed .. "/" .. total .. ")")
        print("^2[QUICK TEST] ^7QBCore shared systém funguje správně!")
    else
        print("^1[QUICK TEST] ^7❌ NĚKTERÉ TESTY SELHALY! (" .. passed .. "/" .. total .. ")")
        print("^1[QUICK TEST] ^7Zkontroluj qbx_core bridge a shared soubory!")
    end
    print("^2[QUICK TEST] ^7" .. string.rep("=", 50))
    
    return results, passed, total
end

-- Registrace příkazu
RegisterCommand('quicktest', function()
    QuickTest()
end, false)

-- Export pro použití z jiných scriptů
_G.QuickTest = QuickTest

-- Automatické spuštění při načtení (s delším čekáním)
CreateThread(function()
    Wait(3000)
    print("^2[QUICK TEST] ^7Použij /quicktest pro rychlé ověření funkčnosti QBCore shared")
end)
