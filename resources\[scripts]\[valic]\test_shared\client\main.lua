-- Test script pro QBX shared funkčnost - CLIENT SIDE
local QBX = exports.qbx_core:GetCoreObject()

-- Funkce pro testování vehicles.lua
local function TestVehicles()
    print("^2[TEST SHARED] ^7Testování vehicles.lua...")
    
    -- Test 1: Načtení všech vozidel
    local allVehicles = exports.qbx_core:GetVehiclesByName()
    local vehicleCount = 0
    for _ in pairs(allVehicles) do
        vehicleCount = vehicleCount + 1
    end
    print(string.format("^3[VEHICLES] ^7Načteno %d vozidel", vehicleCount))
    
    -- Test 2: Načtení konkrétního vozidla podle názvu
    local testVehicle = exports.qbx_core:GetVehiclesByName('sunrise1')
    if testVehicle then
        print(string.format("^3[VEHICLES] ^7Test vozidlo 'sunrise1': %s - %s (%s)", 
            testVehicle.brand, testVehicle.name, testVehicle.model))
        print(string.format("^3[VEHICLES] ^7Cena: $%d, Kategorie: %s, Hash: %s", 
            testVehicle.price, testVehicle.category, testVehicle.hash))
    else
        print("^1[VEHICLES] ^7CHYBA: Vozidlo 'sunrise1' nenalezeno!")
    end
    
    -- Test 3: Načtení vozidel podle hash
    local vehiclesByHash = exports.qbx_core:GetVehiclesByHash()
    local hashCount = 0
    for _ in pairs(vehiclesByHash) do
        hashCount = hashCount + 1
    end
    print(string.format("^3[VEHICLES] ^7Vozidla podle hash: %d", hashCount))
    
    -- Test 4: Načtení vozidel podle kategorie
    local vehiclesByCategory = exports.qbx_core:GetVehiclesByCategory()
    print("^3[VEHICLES] ^7Kategorie vozidel:")
    for category, vehicles in pairs(vehiclesByCategory) do
        print(string.format("^3[VEHICLES] ^7  - %s: %d vozidel", category, #vehicles))
    end
    
    -- Test 5: Test konkrétního hash
    if testVehicle and testVehicle.hash then
        local vehicleByHash = exports.qbx_core:GetVehiclesByHash(testVehicle.hash)
        if vehicleByHash then
            print(string.format("^3[VEHICLES] ^7Vozidlo podle hash %s: %s", 
                testVehicle.hash, vehicleByHash.name))
        else
            print("^1[VEHICLES] ^7CHYBA: Vozidlo podle hash nenalezeno!")
        end
    end
    
    print("^2[VEHICLES] ^7Test dokončen!")
end

-- Funkce pro testování weapons.lua
local function TestWeapons()
    print("^2[TEST SHARED] ^7Testování weapons.lua...")
    
    local allWeapons = exports.qbx_core:GetWeapons()
    local weaponCount = 0
    for _ in pairs(allWeapons) do
        weaponCount = weaponCount + 1
    end
    print(string.format("^3[WEAPONS] ^7Načteno %d zbraní", weaponCount))
    
    -- Test konkrétní zbraně (pokud existuje)
    local testWeaponHash = GetHashKey('WEAPON_PISTOL')
    local testWeapon = exports.qbx_core:GetWeapons(testWeaponHash)
    if testWeapon then
        print(string.format("^3[WEAPONS] ^7Test zbraň WEAPON_PISTOL: %s", testWeapon.label or "Bez názvu"))
    else
        print("^1[WEAPONS] ^7CHYBA: WEAPON_PISTOL nenalezena!")
    end
    
    print("^2[WEAPONS] ^7Test dokončen!")
end

-- Funkce pro testování locations.lua
local function TestLocations()
    print("^2[TEST SHARED] ^7Testování locations.lua...")

    local allLocations = exports.qbx_core:GetLocations()
    local locationCount = 0
    for _ in pairs(allLocations) do
        locationCount = locationCount + 1
    end
    print(string.format("^3[LOCATIONS] ^7Načteno %d lokací", locationCount))

    print("^2[LOCATIONS] ^7Test dokončen!")
end

-- Funkce pro testování jobs.lua
local function TestJobs()
    print("^2[TEST SHARED] ^7Testování jobs.lua...")

    -- Načtení jobs přes require (pokud je dostupný)
    local success, jobs = pcall(function()
        return require('qbx_core/shared/jobs')
    end)

    if success and jobs then
        local jobCount = 0
        for jobName, jobData in pairs(jobs) do
            jobCount = jobCount + 1
            print(string.format("^3[JOBS] ^7Job: %s (%s)", jobName, jobData.label or "Bez názvu"))

            if jobData.grades then
                local gradeCount = 0
                for _ in pairs(jobData.grades) do
                    gradeCount = gradeCount + 1
                end
                print(string.format("^3[JOBS] ^7  - Počet gradů: %d", gradeCount))
            end
        end
        print(string.format("^3[JOBS] ^7Celkem načteno %d jobů", jobCount))
    else
        print("^1[JOBS] ^7CHYBA: Nepodařilo se načíst jobs.lua!")
    end

    print("^2[JOBS] ^7Test dokončen!")
end

-- Funkce pro testování gangs.lua
local function TestGangs()
    print("^2[TEST SHARED] ^7Testování gangs.lua...")

    -- Načtení gangs přes require (pokud je dostupný)
    local success, gangs = pcall(function()
        return require('qbx_core/shared/gangs')
    end)

    if success and gangs then
        local gangCount = 0
        for gangName, gangData in pairs(gangs) do
            gangCount = gangCount + 1
            print(string.format("^3[GANGS] ^7Gang: %s (%s)", gangName, gangData.label or "Bez názvu"))

            if gangData.grades then
                local gradeCount = 0
                for _ in pairs(gangData.grades) do
                    gradeCount = gradeCount + 1
                end
                print(string.format("^3[GANGS] ^7  - Počet gradů: %d", gradeCount))
            end
        end
        print(string.format("^3[GANGS] ^7Celkem načteno %d gangů", gangCount))
    else
        print("^1[GANGS] ^7CHYBA: Nepodařilo se načíst gangs.lua nebo žádné gangy nejsou definovány!")
    end

    print("^2[GANGS] ^7Test dokončen!")
end

-- Funkce pro testování QBX.Shared přístupu
local function TestQBXShared()
    print("^2[TEST SHARED] ^7Testování přímého přístupu k QBX.Shared...")
    
    if QBX and QBX.Shared then
        print("^3[QBX.SHARED] ^7QBX.Shared je dostupný")
        
        if QBX.Shared.Vehicles then
            local count = 0
            for _ in pairs(QBX.Shared.Vehicles) do
                count = count + 1
            end
            print(string.format("^3[QBX.SHARED] ^7QBX.Shared.Vehicles: %d vozidel", count))
        end
        
        if QBX.Shared.VehicleHashes then
            local count = 0
            for _ in pairs(QBX.Shared.VehicleHashes) do
                count = count + 1
            end
            print(string.format("^3[QBX.SHARED] ^7QBX.Shared.VehicleHashes: %d hash", count))
        end
        
        if QBX.Shared.Weapons then
            local count = 0
            for _ in pairs(QBX.Shared.Weapons) do
                count = count + 1
            end
            print(string.format("^3[QBX.SHARED] ^7QBX.Shared.Weapons: %d zbraní", count))
        end
        
        if QBX.Shared.Locations then
            local count = 0
            for _ in pairs(QBX.Shared.Locations) do
                count = count + 1
            end
            print(string.format("^3[QBX.SHARED] ^7QBX.Shared.Locations: %d lokací", count))
        end
    else
        print("^1[QBX.SHARED] ^7CHYBA: QBX.Shared není dostupný!")
    end
    
    print("^2[QBX.SHARED] ^7Test dokončen!")
end

-- Hlavní testovací funkce
local function RunAllTests()
    print("^2=== QBX SHARED TESTY ZAČÍNAJÍ ===^7")
    print("^2[TEST SHARED] ^7Spouštění všech testů...")

    Wait(1000) -- Počkáme, až se vše načte

    TestVehicles()
    Wait(500)

    TestWeapons()
    Wait(500)

    TestLocations()
    Wait(500)

    TestJobs()
    Wait(500)

    TestGangs()
    Wait(500)

    TestQBXShared()

    print("^2=== QBX SHARED TESTY DOKONČENY ===^7")
end

-- Registrace příkazů
RegisterCommand('testshared', function()
    RunAllTests()
end, false)

RegisterCommand('testvehicles', function()
    TestVehicles()
end, false)

RegisterCommand('testweapons', function()
    TestWeapons()
end, false)

RegisterCommand('testlocations', function()
    TestLocations()
end, false)

RegisterCommand('testjobs', function()
    TestJobs()
end, false)

RegisterCommand('testgangs', function()
    TestGangs()
end, false)

-- Automatické spuštění při načtení
CreateThread(function()
    Wait(5000) -- Počkáme 5 sekund po načtení
    print("^2[TEST SHARED] ^7Script načten! Použij /testshared pro spuštění všech testů")
    print("^2[TEST SHARED] ^7Dostupné příkazy: /testshared, /testvehicles, /testweapons, /testlocations, /testjobs, /testgangs")
end)
