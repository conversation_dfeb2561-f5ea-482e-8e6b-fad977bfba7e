-- Test script pro QBCore shared funkčnost (QBX bridge) - CLIENT SIDE
local QBCore = exports['qb-core']:GetCoreObject()

-- Funkce pro testování vehicles.lua
local function TestVehicles()
    print("^2[TEST SHARED] ^7Testování vehicles.lua...")

    if QBCore and QBCore.Shared and QBCore.Shared.Vehicles then
        local vehicleCount = 0
        local testVehicle = nil

        for model, vehicle in pairs(QBCore.Shared.Vehicles) do
            vehicleCount = vehicleCount + 1
            if not testVehicle then
                testVehicle = vehicle
                testVehicle.model = model
                print(string.format("^3[VEHICLES] ^7Test vozidlo '%s': %s - %s",
                    model, vehicle.brand or "N/A", vehicle.name or "N/A"))
                if vehicle.price then
                    print(string.format("^3[VEHICLES] ^7Cena: $%d", vehicle.price))
                end
                if vehicle.category then
                    print(string.format("^3[VEHICLES] ^7Kategorie: %s", vehicle.category))
                end
                if vehicle.hash then
                    print(string.format("^3[VEHICLES] ^7Hash: %s", vehicle.hash))
                end
            end
        end

        print(string.format("^3[VEHICLES] ^7Celkem načteno %d vozidel", vehicleCount))

        -- Test kategorií
        local categories = {}
        for _, vehicle in pairs(QBCore.Shared.Vehicles) do
            if vehicle.category then
                categories[vehicle.category] = (categories[vehicle.category] or 0) + 1
            end
        end

        print("^3[VEHICLES] ^7Kategorie vozidel:")
        for category, count in pairs(categories) do
            print(string.format("^3[VEHICLES] ^7  - %s: %d vozidel", category, count))
        end
    else
        print("^1[VEHICLES] ^7CHYBA: QBCore.Shared.Vehicles není dostupný!")
    end

    print("^2[VEHICLES] ^7Test dokončen!")
end

-- Funkce pro testování weapons.lua
local function TestWeapons()
    print("^2[TEST SHARED] ^7Testování weapons.lua...")

    if QBCore and QBCore.Shared and QBCore.Shared.Weapons then
        local weaponCount = 0
        local testWeapon = nil

        for weaponHash, weapon in pairs(QBCore.Shared.Weapons) do
            weaponCount = weaponCount + 1
            if not testWeapon then
                testWeapon = weapon
                print(string.format("^3[WEAPONS] ^7Test zbraň %s: %s",
                    weaponHash, weapon.label or weapon.name or "Bez názvu"))
            end
        end

        print(string.format("^3[WEAPONS] ^7Celkem načteno %d zbraní", weaponCount))
    else
        print("^1[WEAPONS] ^7CHYBA: QBCore.Shared.Weapons není dostupný!")
    end

    print("^2[WEAPONS] ^7Test dokončen!")
end

-- Funkce pro testování locations.lua
local function TestLocations()
    print("^2[TEST SHARED] ^7Testování locations.lua...")

    if QBCore and QBCore.Shared and QBCore.Shared.Locations then
        local locationCount = 0
        for _ in pairs(QBCore.Shared.Locations) do
            locationCount = locationCount + 1
        end
        print(string.format("^3[LOCATIONS] ^7Načteno %d lokací", locationCount))
    else
        print("^1[LOCATIONS] ^7CHYBA: QBCore.Shared.Locations není dostupný!")
    end

    print("^2[LOCATIONS] ^7Test dokončen!")
end

-- Funkce pro testování jobs.lua
local function TestJobs()
    print("^2[TEST SHARED] ^7Testování jobs.lua...")

    if QBCore and QBCore.Shared and QBCore.Shared.Jobs then
        local jobCount = 0
        for jobName, jobData in pairs(QBCore.Shared.Jobs) do
            jobCount = jobCount + 1
            print(string.format("^3[JOBS] ^7Job: %s (%s)", jobName, jobData.label or "Bez názvu"))

            if jobData.grades then
                local gradeCount = 0
                for _ in pairs(jobData.grades) do
                    gradeCount = gradeCount + 1
                end
                print(string.format("^3[JOBS] ^7  - Počet gradů: %d", gradeCount))
            end
        end
        print(string.format("^3[JOBS] ^7Celkem načteno %d jobů", jobCount))
    else
        print("^1[JOBS] ^7CHYBA: QBCore.Shared.Jobs není dostupný!")
    end

    print("^2[JOBS] ^7Test dokončen!")
end

-- Funkce pro testování gangs.lua
local function TestGangs()
    print("^2[TEST SHARED] ^7Testování gangs.lua...")

    if QBCore and QBCore.Shared and QBCore.Shared.Gangs then
        local gangCount = 0
        for gangName, gangData in pairs(QBCore.Shared.Gangs) do
            gangCount = gangCount + 1
            print(string.format("^3[GANGS] ^7Gang: %s (%s)", gangName, gangData.label or "Bez názvu"))

            if gangData.grades then
                local gradeCount = 0
                for _ in pairs(gangData.grades) do
                    gradeCount = gradeCount + 1
                end
                print(string.format("^3[GANGS] ^7  - Počet gradů: %d", gradeCount))
            end
        end
        print(string.format("^3[GANGS] ^7Celkem načteno %d gangů", gangCount))
    else
        print("^1[GANGS] ^7CHYBA: QBCore.Shared.Gangs není dostupný nebo žádné gangy nejsou definovány!")
    end

    print("^2[GANGS] ^7Test dokončen!")
end

-- Funkce pro testování QBCore.Shared přístupu
local function TestQBCoreShared()
    print("^2[TEST SHARED] ^7Testování přímého přístupu k QBCore.Shared...")

    if QBCore and QBCore.Shared then
        print("^3[QBCORE.SHARED] ^7QBCore.Shared je dostupný")

        -- Test všech shared komponent
        local components = {'Vehicles', 'Weapons', 'Jobs', 'Gangs', 'Locations', 'Items'}
        for _, component in ipairs(components) do
            if QBCore.Shared[component] then
                local count = 0
                for _ in pairs(QBCore.Shared[component]) do
                    count = count + 1
                end
                print(string.format("^3[QBCORE.SHARED] ^7QBCore.Shared.%s: %d položek", component, count))
            else
                print(string.format("^1[QBCORE.SHARED] ^7QBCore.Shared.%s není dostupný!", component))
            end
        end
    else
        print("^1[QBCORE.SHARED] ^7CHYBA: QBCore.Shared není dostupný!")
    end

    print("^2[QBCORE.SHARED] ^7Test dokončen!")
end

-- Hlavní testovací funkce
local function RunAllTests()
    print("^2=== QBCORE SHARED TESTY ZAČÍNAJÍ ===^7")
    print("^2[TEST SHARED] ^7Spouštění všech testů...")

    Wait(1000) -- Počkáme, až se vše načte

    TestVehicles()
    Wait(500)

    TestWeapons()
    Wait(500)

    TestLocations()
    Wait(500)

    TestJobs()
    Wait(500)

    TestGangs()
    Wait(500)

    TestQBCoreShared()

    print("^2=== QBCORE SHARED TESTY DOKONČENY ===^7")
end

-- Registrace příkazů
RegisterCommand('testshared', function()
    RunAllTests()
end, false)

RegisterCommand('testvehicles', function()
    TestVehicles()
end, false)

RegisterCommand('testweapons', function()
    TestWeapons()
end, false)

RegisterCommand('testlocations', function()
    TestLocations()
end, false)

RegisterCommand('testjobs', function()
    TestJobs()
end, false)

RegisterCommand('testgangs', function()
    TestGangs()
end, false)

-- Automatické spuštění při načtení
CreateThread(function()
    Wait(5000) -- Počkáme 5 sekund po načtení
    print("^2[TEST SHARED] ^7Script načten! Použij /testshared pro spuštění všech testů")
    print("^2[TEST SHARED] ^7Dostupné příkazy: /testshared, /testvehicles, /testweapons, /testlocations, /testjobs, /testgangs")
end)
