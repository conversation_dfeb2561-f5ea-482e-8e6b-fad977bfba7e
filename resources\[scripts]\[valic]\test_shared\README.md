# QBX Shared Test Script

Tento script testuje funkčnost QBX shared systému, zejména:
- vehicles.lua
- weapons.lua  
- jobs.lua
- gangs.lua
- locations.lua
- QBX.Shared objekt

## Instalace

1. Zkopíruj složku `test_shared` do `resources/[scripts]/[valic]/`
2. Přidej do server.cfg: `ensure test_shared`
3. Restartuj server

## Použití

### Client příkazy (v hře):
- `/quicktest` - ⚡ Rychlý test (doporučeno pro první spuštění)
- `/testshared` - Spustí všechny základní testy
- `/testvehicles` - Testuje pouze vehicles.lua
- `/testweapons` - Testuje pouze weapons.lua
- `/testlocations` - Testuje pouze locations.lua
- `/testjobs` - Testuje pouze jobs.lua
- `/testgangs` - Testuje pouze gangs.lua
- `/testintegrity` - Spustí testy integrity dat
- `/testperformance` - Spustí performance testy
- `/stresstest` - Spust<PERSON> stress test (10000 iterací)

### Server příkazy (v konzoli):
- `testsharedserver` - Spustí všechny server testy

## Co script testuje

### Vehicles.lua:
- ✅ Načtení všech vozidel
- ✅ Vyhledání vozidla podle názvu
- ✅ Vyhledání vozidla podle hash
- ✅ Kategorizace vozidel
- ✅ Export funkce GetVehiclesByName()
- ✅ Export funkce GetVehiclesByHash()
- ✅ Export funkce GetVehiclesByCategory()

### Weapons.lua:
- ✅ Načtení všech zbraní
- ✅ Export funkce GetWeapons()
- ✅ Test konkrétní zbraně

### Locations.lua:
- ✅ Načtení všech lokací
- ✅ Export funkce GetLocations()

### Jobs.lua:
- ✅ Načtení všech jobů
- ✅ Kontrola struktury jobů
- ✅ Validace gradů

### Gangs.lua:
- ✅ Načtení všech gangů
- ✅ Kontrola struktury gangů
- ✅ Validace gradů

### QBX.Shared objekt:
- ✅ Dostupnost QBX objektu
- ✅ Dostupnost QBX.Shared
- ✅ Přístup k shared datům

### Server testy:
- ✅ Načítání shared souborů
- ✅ Funkčnost exportů na serveru
- ✅ QBX objekt na serveru

### Integrity testy:
- ✅ Kontrola povinných polí vozidel
- ✅ Validace hash konzistence
- ✅ Detekce duplicitních hash
- ✅ Kontrola kategorií vozidel
- ✅ Analýza cen vozidel

### Performance testy:
- ✅ Měření rychlosti export funkcí
- ✅ Porovnání přímého přístupu vs exportů
- ✅ Analýza využití paměti
- ✅ Stress test s 10000 iteracemi

## Výstup

Script vypíše detailní informace o:
- Počtu načtených vozidel, zbraní, lokací
- Funkčnosti jednotlivých exportů
- Chybách při načítání
- Dostupnosti shared dat

## Automatické spuštění

Script se automaticky spustí:
- **Client**: 5 sekund po načtení
- **Server**: 10 sekund po startu serveru

## Troubleshooting

Pokud vidíš chyby:
1. Zkontroluj, že qbx_core běží
2. Zkontroluj, že shared soubory existují
3. Zkontroluj konzoli pro detailní chyby
4. Restartuj qbx_core a test_shared

## Poznámky

- Script je určen pro testování před úpravami shared souborů
- Všechny testy jsou read-only, nic nemodifikují
- Výstup je barevně označený pro lepší čitelnost
- Zelená = úspěch, Červená = chyba, Žlutá = informace
