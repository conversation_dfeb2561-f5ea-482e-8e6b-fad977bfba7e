-- Test script pro QBCore shared funkčnost (QBX bridge) - SERVER SIDE
local QBCore = exports['qb-core']:GetCoreObject()

-- Funkce pro načtení shared dat na serveru
local function LoadSharedData()
    print("^2[TEST SHARED SERVER] ^7Načítání shared dat...")
    
    -- Test QBCore.Shared objektu
    if QBCore and QBCore.Shared then
        print("^3[SERVER] ^7QBCore.Shared objekt je dostupný")

        if QBCore.Shared.Vehicles then
            print("^3[SERVER] ^7QBCore.Shared.Vehicles je dostupný")
        else
            print("^1[SERVER] ^7CHYBA: QBCore.Shared.Vehicles není dostupný!")
        end

        if QBCore.Shared.Jobs then
            print("^3[SERVER] ^7QBCore.Shared.Jobs je dostupný")
        else
            print("^1[SERVER] ^7CHYBA: QBCore.Shared.Jobs není dostupný!")
        end

        if QBCore.Shared.Gang<PERSON> then
            print("^3[SERVER] ^7QBCore.Shared.<PERSON>s je dostupný")
        else
            print("^1[SERVER] ^7CHYBA: QBCore.Shared.Gangs není dostupný!")
        end

        if QBCore.Shared.Weapons then
            print("^3[SERVER] ^7QBCore.Shared.Weapons je dostupný")
        else
            print("^1[SERVER] ^7CHYBA: QBCore.Shared.Weapons není dostupný!")
        end

        if QBCore.Shared.Locations then
            print("^3[SERVER] ^7QBCore.Shared.Locations je dostupný")
        else
            print("^1[SERVER] ^7CHYBA: QBCore.Shared.Locations není dostupný!")
        end
    else
        print("^1[SERVER] ^7CHYBA: QBCore.Shared objekt není dostupný!")
    end
end

-- Funkce pro testování vehicles na serveru
local function TestVehiclesServer()
    print("^2[TEST SHARED SERVER] ^7Testování vehicles na serveru...")

    -- Test QBCore.Shared.Vehicles
    if QBCore and QBCore.Shared and QBCore.Shared.Vehicles then
        local count = 0
        for _ in pairs(QBCore.Shared.Vehicles) do
            count = count + 1
        end
        print(string.format("^3[SERVER VEHICLES] ^7QBCore.Shared.Vehicles: %d vozidel", count))

        -- Test konkrétního vozidla
        local testVehicle = nil
        for model, vehicle in pairs(QBCore.Shared.Vehicles) do
            testVehicle = vehicle
            print(string.format("^3[SERVER VEHICLES] ^7Test vozidlo '%s': %s - %s (%s)",
                model, vehicle.brand or "N/A", vehicle.name or "N/A", vehicle.model or model))
            break -- Jen první vozidlo pro test
        end
    else
        print("^1[SERVER VEHICLES] ^7CHYBA: QBCore.Shared.Vehicles není dostupný!")
    end

    print("^2[SERVER VEHICLES] ^7Test dokončen!")
end

-- Funkce pro testování weapons na serveru
local function TestWeaponsServer()
    print("^2[TEST SHARED SERVER] ^7Testování weapons na serveru...")

    if QBCore and QBCore.Shared and QBCore.Shared.Weapons then
        local count = 0
        for _ in pairs(QBCore.Shared.Weapons) do
            count = count + 1
        end
        print(string.format("^3[SERVER WEAPONS] ^7QBCore.Shared.Weapons: %d zbraní", count))
    else
        print("^1[SERVER WEAPONS] ^7CHYBA: QBCore.Shared.Weapons není dostupný!")
    end

    print("^2[SERVER WEAPONS] ^7Test dokončen!")
end

-- Funkce pro testování jobs na serveru
local function TestJobsServer()
    print("^2[TEST SHARED SERVER] ^7Testování jobs na serveru...")

    if QBCore and QBCore.Shared and QBCore.Shared.Jobs then
        local count = 0
        for jobName, jobData in pairs(QBCore.Shared.Jobs) do
            count = count + 1
            print(string.format("^3[SERVER JOBS] ^7Job: %s (%s)", jobName, jobData.label or "Bez názvu"))
        end
        print(string.format("^3[SERVER JOBS] ^7QBCore.Shared.Jobs: %d jobů", count))
    else
        print("^1[SERVER JOBS] ^7CHYBA: QBCore.Shared.Jobs není dostupný!")
    end

    print("^2[SERVER JOBS] ^7Test dokončen!")
end

-- Funkce pro testování gangs na serveru
local function TestGangsServer()
    print("^2[TEST SHARED SERVER] ^7Testování gangs na serveru...")

    if QBCore and QBCore.Shared and QBCore.Shared.Gangs then
        local count = 0
        for gangName, gangData in pairs(QBCore.Shared.Gangs) do
            count = count + 1
            print(string.format("^3[SERVER GANGS] ^7Gang: %s (%s)", gangName, gangData.label or "Bez názvu"))
        end
        print(string.format("^3[SERVER GANGS] ^7QBCore.Shared.Gangs: %d gangů", count))
    else
        print("^1[SERVER GANGS] ^7CHYBA: QBCore.Shared.Gangs není dostupný!")
    end

    print("^2[SERVER GANGS] ^7Test dokončen!")
end

-- Funkce pro testování locations na serveru
local function TestLocationsServer()
    print("^2[TEST SHARED SERVER] ^7Testování locations na serveru...")

    if QBCore and QBCore.Shared and QBCore.Shared.Locations then
        local count = 0
        for _ in pairs(QBCore.Shared.Locations) do
            count = count + 1
        end
        print(string.format("^3[SERVER LOCATIONS] ^7QBCore.Shared.Locations: %d lokací", count))
    else
        print("^1[SERVER LOCATIONS] ^7CHYBA: QBCore.Shared.Locations není dostupný!")
    end

    print("^2[SERVER LOCATIONS] ^7Test dokončen!")
end

-- Funkce pro testování QBCore objektu na serveru
local function TestQBCoreServer()
    print("^2[TEST SHARED SERVER] ^7Testování QBCore objektu na serveru...")

    if QBCore then
        print("^3[SERVER QBCORE] ^7QBCore objekt je dostupný na serveru")

        if QBCore.Shared then
            print("^3[SERVER QBCORE] ^7QBCore.Shared je dostupný")

            -- Test všech shared komponent
            local components = {'Vehicles', 'Weapons', 'Jobs', 'Gangs', 'Locations', 'Items'}
            for _, component in ipairs(components) do
                if QBCore.Shared[component] then
                    local count = 0
                    for _ in pairs(QBCore.Shared[component]) do
                        count = count + 1
                    end
                    print(string.format("^3[SERVER QBCORE] ^7QBCore.Shared.%s: %d položek", component, count))
                else
                    print(string.format("^1[SERVER QBCORE] ^7QBCore.Shared.%s není dostupný!", component))
                end
            end
        else
            print("^1[SERVER QBCORE] ^7CHYBA: QBCore.Shared není dostupný na serveru!")
        end
    else
        print("^1[SERVER QBCORE] ^7CHYBA: QBCore objekt není dostupný na serveru!")
    end

    print("^2[SERVER QBCORE] ^7Test dokončen!")
end

-- Hlavní testovací funkce pro server
local function RunAllServerTests()
    print("^2=== QBCORE SHARED SERVER TESTY ZAČÍNAJÍ ===^7")

    LoadSharedData()
    Wait(1000)

    TestVehiclesServer()
    Wait(500)

    TestWeaponsServer()
    Wait(500)

    TestJobsServer()
    Wait(500)

    TestGangsServer()
    Wait(500)

    TestLocationsServer()
    Wait(500)

    TestQBCoreServer()

    print("^2=== QBCORE SHARED SERVER TESTY DOKONČENY ===^7")
end

-- Registrace příkazů pro server
RegisterCommand('testsharedserver', function(source)
    if source == 0 then -- Pouze z konzole
        RunAllServerTests()
    end
end, true)

-- Event pro testování z klienta
RegisterNetEvent('test_shared:runServerTests', function()
    RunAllServerTests()
end)

-- Automatické spuštění při startu serveru
CreateThread(function()
    Wait(10000) -- Počkáme 10 sekund po startu
    print("^2[TEST SHARED SERVER] ^7Server script načten!")
    print("^2[TEST SHARED SERVER] ^7Použij 'testsharedserver' v konzoli pro spuštění testů")
    
    -- Automatické spuštění testů
    RunAllServerTests()
end)
