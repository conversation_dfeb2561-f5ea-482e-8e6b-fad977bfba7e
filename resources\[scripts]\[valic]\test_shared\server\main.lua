-- Test script pro QBX shared funkčnost - SERVER SIDE
local QBX = exports.qbx_core:GetCoreObject()

-- Funkce pro načtení shared dat na serveru
local function LoadSharedData()
    print("^2[TEST SHARED SERVER] ^7Načítání shared dat...")
    
    -- Načtení vehicles
    local vehiclesPath = GetResourcePath('qbx_core') .. '/shared/vehicles.lua'
    local vehicles = LoadResourceFile('qbx_core', 'shared/vehicles.lua')
    if vehicles then
        print("^3[SERVER] ^7vehicles.lua úspěšně načten")
    else
        print("^1[SERVER] ^7CHYBA: vehicles.lua se nepodařilo načíst!")
    end
    
    -- Načtení jobs
    local jobs = LoadResourceFile('qbx_core', 'shared/jobs.lua')
    if jobs then
        print("^3[SERVER] ^7jobs.lua úspěšně načten")
    else
        print("^1[SERVER] ^7CHYBA: jobs.lua se nepodařilo načíst!")
    end
    
    -- Načtení gangs
    local gangs = LoadResourceFile('qbx_core', 'shared/gangs.lua')
    if gangs then
        print("^3[SERVER] ^7gangs.lua úspěšně načten")
    else
        print("^1[SERVER] ^7CHYBA: gangs.lua se nepodařilo načíst!")
    end
    
    -- Načtení weapons
    local weapons = LoadResourceFile('qbx_core', 'shared/weapons.lua')
    if weapons then
        print("^3[SERVER] ^7weapons.lua úspěšně načten")
    else
        print("^1[SERVER] ^7CHYBA: weapons.lua se nepodařilo načíst!")
    end
    
    -- Načtení locations
    local locations = LoadResourceFile('qbx_core', 'shared/locations.lua')
    if locations then
        print("^3[SERVER] ^7locations.lua úspěšně načten")
    else
        print("^1[SERVER] ^7CHYBA: locations.lua se nepodařilo načíst!")
    end
end

-- Funkce pro testování vehicles na serveru
local function TestVehiclesServer()
    print("^2[TEST SHARED SERVER] ^7Testování vehicles na serveru...")
    
    -- Test exportů
    local allVehicles = exports.qbx_core:GetVehiclesByName()
    if allVehicles then
        local count = 0
        for _ in pairs(allVehicles) do
            count = count + 1
        end
        print(string.format("^3[SERVER VEHICLES] ^7Export GetVehiclesByName: %d vozidel", count))
    else
        print("^1[SERVER VEHICLES] ^7CHYBA: Export GetVehiclesByName nefunguje!")
    end
    
    -- Test konkrétního vozidla
    local testVehicle = exports.qbx_core:GetVehiclesByName('sunrise1')
    if testVehicle then
        print(string.format("^3[SERVER VEHICLES] ^7Test vozidlo 'sunrise1': %s - %s", 
            testVehicle.brand, testVehicle.name))
    else
        print("^1[SERVER VEHICLES] ^7CHYBA: Vozidlo 'sunrise1' nenalezeno na serveru!")
    end
    
    -- Test hash
    local vehiclesByHash = exports.qbx_core:GetVehiclesByHash()
    if vehiclesByHash then
        local count = 0
        for _ in pairs(vehiclesByHash) do
            count = count + 1
        end
        print(string.format("^3[SERVER VEHICLES] ^7Export GetVehiclesByHash: %d hash", count))
    else
        print("^1[SERVER VEHICLES] ^7CHYBA: Export GetVehiclesByHash nefunguje!")
    end
    
    -- Test kategorií
    local vehiclesByCategory = exports.qbx_core:GetVehiclesByCategory()
    if vehiclesByCategory then
        local categoryCount = 0
        for category, vehicles in pairs(vehiclesByCategory) do
            categoryCount = categoryCount + 1
        end
        print(string.format("^3[SERVER VEHICLES] ^7Export GetVehiclesByCategory: %d kategorií", categoryCount))
    else
        print("^1[SERVER VEHICLES] ^7CHYBA: Export GetVehiclesByCategory nefunguje!")
    end
    
    print("^2[SERVER VEHICLES] ^7Test dokončen!")
end

-- Funkce pro testování weapons na serveru
local function TestWeaponsServer()
    print("^2[TEST SHARED SERVER] ^7Testování weapons na serveru...")
    
    local allWeapons = exports.qbx_core:GetWeapons()
    if allWeapons then
        local count = 0
        for _ in pairs(allWeapons) do
            count = count + 1
        end
        print(string.format("^3[SERVER WEAPONS] ^7Export GetWeapons: %d zbraní", count))
    else
        print("^1[SERVER WEAPONS] ^7CHYBA: Export GetWeapons nefunguje!")
    end
    
    print("^2[SERVER WEAPONS] ^7Test dokončen!")
end

-- Funkce pro testování locations na serveru
local function TestLocationsServer()
    print("^2[TEST SHARED SERVER] ^7Testování locations na serveru...")
    
    local allLocations = exports.qbx_core:GetLocations()
    if allLocations then
        local count = 0
        for _ in pairs(allLocations) do
            count = count + 1
        end
        print(string.format("^3[SERVER LOCATIONS] ^7Export GetLocations: %d lokací", count))
    else
        print("^1[SERVER LOCATIONS] ^7CHYBA: Export GetLocations nefunguje!")
    end
    
    print("^2[SERVER LOCATIONS] ^7Test dokončen!")
end

-- Funkce pro testování QBX objektu na serveru
local function TestQBXServer()
    print("^2[TEST SHARED SERVER] ^7Testování QBX objektu na serveru...")
    
    if QBX then
        print("^3[SERVER QBX] ^7QBX objekt je dostupný na serveru")
        
        if QBX.Shared then
            print("^3[SERVER QBX] ^7QBX.Shared je dostupný")
            
            if QBX.Shared.Vehicles then
                local count = 0
                for _ in pairs(QBX.Shared.Vehicles) do
                    count = count + 1
                end
                print(string.format("^3[SERVER QBX] ^7QBX.Shared.Vehicles: %d vozidel", count))
            end
            
            if QBX.Shared.VehicleHashes then
                local count = 0
                for _ in pairs(QBX.Shared.VehicleHashes) do
                    count = count + 1
                end
                print(string.format("^3[SERVER QBX] ^7QBX.Shared.VehicleHashes: %d hash", count))
            end
        else
            print("^1[SERVER QBX] ^7CHYBA: QBX.Shared není dostupný na serveru!")
        end
    else
        print("^1[SERVER QBX] ^7CHYBA: QBX objekt není dostupný na serveru!")
    end
    
    print("^2[SERVER QBX] ^7Test dokončen!")
end

-- Hlavní testovací funkce pro server
local function RunAllServerTests()
    print("^2=== QBX SHARED SERVER TESTY ZAČÍNAJÍ ===^7")
    
    LoadSharedData()
    Wait(1000)
    
    TestVehiclesServer()
    Wait(500)
    
    TestWeaponsServer()
    Wait(500)
    
    TestLocationsServer()
    Wait(500)
    
    TestQBXServer()
    
    print("^2=== QBX SHARED SERVER TESTY DOKONČENY ===^7")
end

-- Registrace příkazů pro server
RegisterCommand('testsharedserver', function(source)
    if source == 0 then -- Pouze z konzole
        RunAllServerTests()
    end
end, true)

-- Event pro testování z klienta
RegisterNetEvent('test_shared:runServerTests', function()
    RunAllServerTests()
end)

-- Automatické spuštění při startu serveru
CreateThread(function()
    Wait(10000) -- Počkáme 10 sekund po startu
    print("^2[TEST SHARED SERVER] ^7Server script načten!")
    print("^2[TEST SHARED SERVER] ^7Použij 'testsharedserver' v konzoli pro spuštění testů")
    
    -- Automatické spuštění testů
    RunAllServerTests()
end)
