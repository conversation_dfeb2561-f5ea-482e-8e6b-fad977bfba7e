-- Pokročilé testy integrity pro QBCore shared data
local QBCore = exports['qb-core']:GetCoreObject()

-- Test integrity vehicles.lua
local function TestVehicleIntegrity()
    print("^2[INTEGRITY] ^7Testování integrity vehicles.lua...")

    if not QBCore or not QBCore.Shared or not QBCore.Shared.Vehicles then
        print("^1[INTEGRITY ERROR] ^7QBCore.Shared.Vehicles není dostupný!")
        return 1, 0
    end

    local allVehicles = QBCore.Shared.Vehicles
    local errors = 0
    local warnings = 0
    
    for model, vehicle in pairs(allVehicles) do
        -- Test povinných polí
        if not vehicle.name then
            print(string.format("^1[INTEGRITY ERROR] ^7Vozidlo '%s' nemá název!", model))
            errors = errors + 1
        end
        
        if not vehicle.brand then
            print(string.format("^1[INTEGRITY ERROR] ^7Vozidlo '%s' nemá značku!", model))
            errors = errors + 1
        end
        
        if not vehicle.model then
            print(string.format("^1[INTEGRITY ERROR] ^7Vozidlo '%s' nemá model!", model))
            errors = errors + 1
        elseif vehicle.model ~= model then
            print(string.format("^3[INTEGRITY WARNING] ^7Vozidlo '%s' má jiný model než klíč: %s", model, vehicle.model))
            warnings = warnings + 1
        end
        
        if not vehicle.price or vehicle.price < 0 then
            print(string.format("^1[INTEGRITY ERROR] ^7Vozidlo '%s' má neplatnou cenu: %s", model, tostring(vehicle.price)))
            errors = errors + 1
        end
        
        if not vehicle.category then
            print(string.format("^1[INTEGRITY ERROR] ^7Vozidlo '%s' nemá kategorii!", model))
            errors = errors + 1
        end
        
        if not vehicle.type then
            print(string.format("^1[INTEGRITY ERROR] ^7Vozidlo '%s' nemá typ!", model))
            errors = errors + 1
        end
        
        if not vehicle.hash then
            print(string.format("^1[INTEGRITY ERROR] ^7Vozidlo '%s' nemá hash!", model))
            errors = errors + 1
        end
        
        -- Test hash konzistence
        local expectedHash = GetHashKey(model)
        if vehicle.hash and vehicle.hash ~= expectedHash then
            print(string.format("^3[INTEGRITY WARNING] ^7Vozidlo '%s' má nekonzistentní hash. Očekáváno: %s, Nalezeno: %s", 
                model, expectedHash, vehicle.hash))
            warnings = warnings + 1
        end
    end
    
    print(string.format("^2[INTEGRITY] ^7Test vehicles dokončen. Chyby: %d, Varování: %d", errors, warnings))
    return errors, warnings
end

-- Test duplicitních hash
local function TestDuplicateHashes()
    print("^2[INTEGRITY] ^7Testování duplicitních hash...")

    if not QBCore or not QBCore.Shared or not QBCore.Shared.Vehicles then
        print("^1[INTEGRITY ERROR] ^7QBCore.Shared.Vehicles není dostupný!")
        return 1
    end

    local allVehicles = QBCore.Shared.Vehicles
    local hashCount = {}
    local duplicates = 0
    
    for model, vehicle in pairs(allVehicles) do
        if vehicle.hash then
            if hashCount[vehicle.hash] then
                print(string.format("^1[INTEGRITY ERROR] ^7Duplicitní hash %s: %s a %s", 
                    vehicle.hash, hashCount[vehicle.hash], model))
                duplicates = duplicates + 1
            else
                hashCount[vehicle.hash] = model
            end
        end
    end
    
    print(string.format("^2[INTEGRITY] ^7Test duplicitních hash dokončen. Duplicity: %d", duplicates))
    return duplicates
end

-- Test kategorií vozidel
local function TestVehicleCategories()
    print("^2[INTEGRITY] ^7Testování kategorií vozidel...")
    
    local validCategories = {
        'compacts', 'sedans', 'suvs', 'coupes', 'muscle', 'sportsclassics',
        'sports', 'super', 'motorcycles', 'offroad', 'industrial', 'utility',
        'vans', 'cycles', 'boats', 'helicopters', 'planes', 'service',
        'emergency', 'military', 'commercial', 'trains'
    }
    
    local categorySet = {}
    for _, cat in ipairs(validCategories) do
        categorySet[cat] = true
    end
    
    local allVehicles = exports.qbx_core:GetVehiclesByName()
    local invalidCategories = 0
    local foundCategories = {}
    
    for model, vehicle in pairs(allVehicles) do
        if vehicle.category then
            foundCategories[vehicle.category] = (foundCategories[vehicle.category] or 0) + 1
            
            if not categorySet[vehicle.category] then
                print(string.format("^3[INTEGRITY WARNING] ^7Vozidlo '%s' má neplatnou kategorii: %s", 
                    model, vehicle.category))
                invalidCategories = invalidCategories + 1
            end
        end
    end
    
    print("^3[INTEGRITY] ^7Nalezené kategorie:")
    for category, count in pairs(foundCategories) do
        local status = categorySet[category] and "✓" or "✗"
        print(string.format("^3[INTEGRITY] ^7  %s %s: %d vozidel", status, category, count))
    end
    
    print(string.format("^2[INTEGRITY] ^7Test kategorií dokončen. Neplatné kategorie: %d", invalidCategories))
    return invalidCategories
end

-- Test cen vozidel
local function TestVehiclePrices()
    print("^2[INTEGRITY] ^7Testování cen vozidel...")
    
    local allVehicles = exports.qbx_core:GetVehiclesByName()
    local priceIssues = 0
    local priceStats = {
        min = math.huge,
        max = 0,
        total = 0,
        count = 0
    }
    
    for model, vehicle in pairs(allVehicles) do
        if vehicle.price then
            priceStats.count = priceStats.count + 1
            priceStats.total = priceStats.total + vehicle.price
            priceStats.min = math.min(priceStats.min, vehicle.price)
            priceStats.max = math.max(priceStats.max, vehicle.price)
            
            -- Kontrola extrémních cen
            if vehicle.price < 1000 then
                print(string.format("^3[INTEGRITY WARNING] ^7Vozidlo '%s' má velmi nízkou cenu: $%d", 
                    model, vehicle.price))
                priceIssues = priceIssues + 1
            elseif vehicle.price > 10000000 then
                print(string.format("^3[INTEGRITY WARNING] ^7Vozidlo '%s' má velmi vysokou cenu: $%d", 
                    model, vehicle.price))
                priceIssues = priceIssues + 1
            end
        end
    end
    
    if priceStats.count > 0 then
        local average = priceStats.total / priceStats.count
        print(string.format("^3[INTEGRITY] ^7Cenové statistiky:"))
        print(string.format("^3[INTEGRITY] ^7  Min: $%d, Max: $%d, Průměr: $%d", 
            priceStats.min, priceStats.max, math.floor(average)))
    end
    
    print(string.format("^2[INTEGRITY] ^7Test cen dokončen. Problémové ceny: %d", priceIssues))
    return priceIssues
end

-- Hlavní funkce pro spuštění všech integrity testů
local function RunIntegrityTests()
    print("^2=== QBX SHARED INTEGRITY TESTY ===^7")
    
    local totalErrors = 0
    local totalWarnings = 0
    
    local errors, warnings = TestVehicleIntegrity()
    totalErrors = totalErrors + errors
    totalWarnings = totalWarnings + warnings
    Wait(500)
    
    local duplicates = TestDuplicateHashes()
    totalErrors = totalErrors + duplicates
    Wait(500)
    
    local invalidCategories = TestVehicleCategories()
    totalWarnings = totalWarnings + invalidCategories
    Wait(500)
    
    local priceIssues = TestVehiclePrices()
    totalWarnings = totalWarnings + priceIssues
    
    print("^2=== INTEGRITY TESTY DOKONČENY ===^7")
    print(string.format("^2[INTEGRITY] ^7Celkem chyb: %d, Celkem varování: %d", totalErrors, totalWarnings))
    
    if totalErrors == 0 and totalWarnings == 0 then
        print("^2[INTEGRITY] ^7✓ Všechny testy prošly bez problémů!")
    elseif totalErrors == 0 then
        print("^3[INTEGRITY] ^7⚠ Testy prošly s varováními")
    else
        print("^1[INTEGRITY] ^7✗ Nalezeny kritické chyby!")
    end
end

-- Export funkcí
RegisterCommand('testintegrity', function()
    RunIntegrityTests()
end, false)

-- Přidání do globálního scope pro použití z jiných souborů
_G.TestVehicleIntegrity = TestVehicleIntegrity
_G.TestDuplicateHashes = TestDuplicateHashes
_G.TestVehicleCategories = TestVehicleCategories
_G.TestVehiclePrices = TestVehiclePrices
_G.RunIntegrityTests = RunIntegrityTests
