-- Performance testy pro QBCore shared funkce
local QBCore = exports['qb-core']:GetCoreObject()

-- Pomocná funkce pro měření času
local function MeasureTime(func, iterations)
    iterations = iterations or 1000
    
    local startTime = GetGameTimer()
    for i = 1, iterations do
        func()
    end
    local endTime = GetGameTimer()
    
    return endTime - startTime, (endTime - startTime) / iterations
end

-- Test výkonu QBCore.Shared.Vehicles
local function TestVehiclesPerformance()
    print("^2[PERFORMANCE] ^7Testování výkonu QBCore.Shared.Vehicles...")

    -- Test načtení všech vozidel
    local totalTime, avgTime = MeasureTime(function()
        if QBCore and QBCore.Shared and QBCore.Shared.Vehicles then
            local _ = QBCore.Shared.Vehicles
        end
    end, 1000)

    print(string.format("^3[PERFORMANCE] ^7QBCore.Shared.Vehicles přístup: %dms celkem, %.2fms průměr",
        totalTime, avgTime))

    -- Test vyhledání konkrétního vozidla
    local totalTime2, avgTime2 = MeasureTime(function()
        if QBCore and QBCore.Shared and QBCore.Shared.Vehicles then
            for model, vehicle in pairs(QBCore.Shared.Vehicles) do
                break -- Jen první vozidlo
            end
        end
    end, 1000)

    print(string.format("^3[PERFORMANCE] ^7Vyhledání prvního vozidla: %dms celkem, %.2fms průměr",
        totalTime2, avgTime2))

    return totalTime, avgTime, totalTime2, avgTime2
end

-- Test výkonu GetVehiclesByHash
local function TestGetVehiclesByHashPerformance()
    print("^2[PERFORMANCE] ^7Testování výkonu GetVehiclesByHash...")
    
    -- Test načtení všech vozidel podle hash
    local totalTime, avgTime = MeasureTime(function()
        exports.qbx_core:GetVehiclesByHash()
    end, 100)
    
    print(string.format("^3[PERFORMANCE] ^7GetVehiclesByHash() (všechna): %dms celkem, %.2fms průměr", 
        totalTime, avgTime))
    
    -- Test načtení konkrétního vozidla podle hash
    local testHash = GetHashKey('sunrise1')
    local totalTime2, avgTime2 = MeasureTime(function()
        exports.qbx_core:GetVehiclesByHash(testHash)
    end, 1000)
    
    print(string.format("^3[PERFORMANCE] ^7GetVehiclesByHash(%s): %dms celkem, %.2fms průměr", 
        testHash, totalTime2, avgTime2))
    
    return totalTime, avgTime, totalTime2, avgTime2
end

-- Test výkonu GetVehiclesByCategory
local function TestGetVehiclesByCategoryPerformance()
    print("^2[PERFORMANCE] ^7Testování výkonu GetVehiclesByCategory...")
    
    local totalTime, avgTime = MeasureTime(function()
        exports.qbx_core:GetVehiclesByCategory()
    end, 100)
    
    print(string.format("^3[PERFORMANCE] ^7GetVehiclesByCategory(): %dms celkem, %.2fms průměr", 
        totalTime, avgTime))
    
    return totalTime, avgTime
end

-- Test výkonu GetWeapons
local function TestGetWeaponsPerformance()
    print("^2[PERFORMANCE] ^7Testování výkonu GetWeapons...")
    
    -- Test načtení všech zbraní
    local totalTime, avgTime = MeasureTime(function()
        exports.qbx_core:GetWeapons()
    end, 100)
    
    print(string.format("^3[PERFORMANCE] ^7GetWeapons() (všechny): %dms celkem, %.2fms průměr", 
        totalTime, avgTime))
    
    -- Test načtení konkrétní zbraně
    local testWeaponHash = GetHashKey('WEAPON_PISTOL')
    local totalTime2, avgTime2 = MeasureTime(function()
        exports.qbx_core:GetWeapons(testWeaponHash)
    end, 1000)
    
    print(string.format("^3[PERFORMANCE] ^7GetWeapons(%s): %dms celkem, %.2fms průměr", 
        testWeaponHash, totalTime2, avgTime2))
    
    return totalTime, avgTime, totalTime2, avgTime2
end

-- Test výkonu přímého přístupu k QBX.Shared
local function TestDirectAccessPerformance()
    print("^2[PERFORMANCE] ^7Testování výkonu přímého přístupu k QBX.Shared...")
    
    if not QBX or not QBX.Shared then
        print("^1[PERFORMANCE] ^7CHYBA: QBX.Shared není dostupný!")
        return
    end
    
    -- Test přístupu k vozidlům
    local totalTime, avgTime = MeasureTime(function()
        local _ = QBX.Shared.Vehicles
    end, 1000)
    
    print(string.format("^3[PERFORMANCE] ^7QBX.Shared.Vehicles přístup: %dms celkem, %.2fms průměr", 
        totalTime, avgTime))
    
    -- Test vyhledání konkrétního vozidla
    local totalTime2, avgTime2 = MeasureTime(function()
        local _ = QBX.Shared.Vehicles['sunrise1']
    end, 1000)
    
    print(string.format("^3[PERFORMANCE] ^7QBX.Shared.Vehicles['sunrise1']: %dms celkem, %.2fms průměr", 
        totalTime2, avgTime2))
    
    -- Test přístupu k hash tabulce
    local totalTime3, avgTime3 = MeasureTime(function()
        local _ = QBX.Shared.VehicleHashes
    end, 1000)
    
    print(string.format("^3[PERFORMANCE] ^7QBX.Shared.VehicleHashes přístup: %dms celkem, %.2fms průměr", 
        totalTime3, avgTime3))
    
    return totalTime, avgTime, totalTime2, avgTime2, totalTime3, avgTime3
end

-- Test memory usage (přibližný)
local function TestMemoryUsage()
    print("^2[PERFORMANCE] ^7Testování využití paměti...")
    
    -- Před testem
    collectgarbage("collect")
    local memBefore = collectgarbage("count")
    
    -- Načtení dat
    local vehicles = exports.qbx_core:GetVehiclesByName()
    local vehiclesByHash = exports.qbx_core:GetVehiclesByHash()
    local vehiclesByCategory = exports.qbx_core:GetVehiclesByCategory()
    local weapons = exports.qbx_core:GetWeapons()
    
    -- Po testu
    local memAfter = collectgarbage("count")
    local memUsed = memAfter - memBefore
    
    print(string.format("^3[PERFORMANCE] ^7Paměť před testem: %.2f KB", memBefore))
    print(string.format("^3[PERFORMANCE] ^7Paměť po testu: %.2f KB", memAfter))
    print(string.format("^3[PERFORMANCE] ^7Použitá paměť: %.2f KB", memUsed))
    
    -- Počet objektů
    local vehicleCount = 0
    for _ in pairs(vehicles) do vehicleCount = vehicleCount + 1 end
    
    local weaponCount = 0
    for _ in pairs(weapons) do weaponCount = weaponCount + 1 end
    
    print(string.format("^3[PERFORMANCE] ^7Načteno %d vozidel, %d zbraní", vehicleCount, weaponCount))
    
    return memUsed, vehicleCount, weaponCount
end

-- Stress test - opakované volání funkcí
local function StressTest()
    print("^2[PERFORMANCE] ^7Spouštění stress testu...")
    
    local iterations = 10000
    local startTime = GetGameTimer()
    
    for i = 1, iterations do
        -- Střídání různých volání
        if i % 4 == 0 then
            exports.qbx_core:GetVehiclesByName('sunrise1')
        elseif i % 4 == 1 then
            exports.qbx_core:GetVehiclesByHash(GetHashKey('sunrise1'))
        elseif i % 4 == 2 then
            exports.qbx_core:GetWeapons(GetHashKey('WEAPON_PISTOL'))
        else
            exports.qbx_core:GetVehiclesByCategory()
        end
        
        -- Občasné čekání aby nedošlo k zamrznutí
        if i % 1000 == 0 then
            Wait(0)
            print(string.format("^3[STRESS] ^7Dokončeno %d/%d iterací", i, iterations))
        end
    end
    
    local endTime = GetGameTimer()
    local totalTime = endTime - startTime
    
    print(string.format("^3[PERFORMANCE] ^7Stress test dokončen: %d iterací za %dms (%.2fms/iterace)", 
        iterations, totalTime, totalTime / iterations))
    
    return totalTime, iterations
end

-- Hlavní funkce pro spuštění všech performance testů
local function RunPerformanceTests()
    print("^2=== QBCORE SHARED PERFORMANCE TESTY ===^7")

    TestVehiclesPerformance()
    Wait(500)

    TestDirectAccessPerformance()
    Wait(500)

    TestMemoryUsage()
    Wait(1000)

    StressTest()

    print("^2=== PERFORMANCE TESTY DOKONČENY ===^7")
end

-- Export funkcí
RegisterCommand('testperformance', function()
    RunPerformanceTests()
end, false)

RegisterCommand('stresstest', function()
    StressTest()
end, false)

-- Přidání do globálního scope
_G.RunPerformanceTests = RunPerformanceTests
_G.StressTest = StressTest
